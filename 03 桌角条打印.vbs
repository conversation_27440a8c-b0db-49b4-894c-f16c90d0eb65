
Sub 桌角条打印()
    Dim ws As Worksheet
    Dim wsOutput As Worksheet
    Dim lastRow As Long
    Dim i As Long
    Dim currentRow As Long
    Dim currentCol As Long
    Dim examRoom As Integer
    Dim seatNumber As Integer
    Dim schoolPoint As String
    Dim prevExamRoom As Integer
    Dim prevSeatNumber As Integer
    Dim prevSchoolPoint As String
    Dim studentInfo As String
    Dim isFirstRecord As Boolean ' 用于标记是否是第一条记录
    Dim lastExamRoomOfPrevSchoolPoint As Integer ' 记录上一个校点的最后一个考场
    Dim combinedInfo As String

    ' 检查是否存在名为“桌角条打印”的工作表，如果存在则删除
    For Each ws In ThisWorkbook.Sheets
        If ws.Name = "桌角条打印" Then
            Application.DisplayAlerts = False ' 关闭警告提示
            ws.Delete
            Application.DisplayAlerts = True ' 打开警告提示
            Exit For
        End If
    Next ws

    ' 设置源工作表和输出工作表
    Set ws = ThisWorkbook.Sheets("学生信息汇总表")
    Set wsOutput = ThisWorkbook.Sheets.Add(After:=ThisWorkbook.Sheets(ThisWorkbook.Sheets.Count))
    wsOutput.Name = "桌角条打印"

    ' 获取源工作表的最后一行
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row

    ' 按学校、校点、考场号和座位号排序
    ws.Sort.SortFields.Clear
    ws.Sort.SortFields.Add key:=ws.Range("A2:A" & lastRow), SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
    ws.Sort.SortFields.Add key:=ws.Range("B2:B" & lastRow), SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
    ws.Sort.SortFields.Add key:=ws.Range("G2:G" & lastRow), SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
    ws.Sort.SortFields.Add key:=ws.Range("H2:H" & lastRow), SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
    With ws.Sort
        .SetRange ws.Range("A1:I" & lastRow)
        .Header = xlYes
        .MatchCase = False
        .Orientation = xlTopToBottom
        .SortMethod = xlPinYin
        .Apply
    End With

    ' 初始化变量
    currentRow = 1
    currentCol = 1
    prevExamRoom = 0
    prevSeatNumber = 0
    prevSchoolPoint = ""
    studentInfo = ""
    isFirstRecord = True ' 标记为第一条记录
    lastExamRoomOfPrevSchoolPoint = 0

    ' 跟踪当前学校
    Dim currentSchoolName As String
    Dim previousSchoolName As String

    previousSchoolName = ""

    ' 遍历源工作表中的每一行
    For i = 2 To lastRow
        Dim schoolName As String
        schoolName = ws.Cells(i, 1).Value ' 学校名称
        schoolPoint = ws.Cells(i, 2).Value ' 校点
        examRoom = ws.Cells(i, 7).Value ' 考场
        seatNumber = ws.Cells(i, 8).Value ' 座位

        ' 检查是否是新校点的第一个考场
        If schoolPoint <> prevSchoolPoint And examRoom = 1 Then
            ' 写入上一校点的最后一组信息
            If Not isFirstRecord Then
                ' 合并考场和学生信息
                combinedInfo = "第" & prevExamRoom & "考场 第" & prevSeatNumber & "桌" & vbCrLf & studentInfo
                ' 去除 combinedInfo 末尾的空白字符
                combinedInfo = Trim(combinedInfo)

                ' 写入合并后的信息
                wsOutput.Cells(currentRow, currentCol).Value = combinedInfo

                With wsOutput.Cells(currentRow, currentCol).Font
                    .Name = "黑体"
                    .Size = 14
                End With
                ' 设置单元格内容左对齐
                wsOutput.Cells(currentRow, currentCol).HorizontalAlignment = xlCenter

                ' 设置组信息的虚线边框
                With wsOutput.Cells(currentRow, currentCol).Resize(1, 1).Borders
                    ' 修改为双线样式
                    .LineStyle = xlDouble
                    .ColorIndex = 0 ' 边框颜色
                    .TintAndShade = 0
                    .Weight = xlThin ' 边框粗细
                End With

                ' 调整单元格格式
                wsOutput.Cells(currentRow, currentCol).RowHeight = 65
                wsOutput.Cells(currentRow, currentCol).ColumnWidth = 43

                ' 设置B列列宽为4个字符大小
                wsOutput.Columns("B").ColumnWidth = 4

                ' 按考场分开打印
                ' 移动到下一个单元格
                currentCol = currentCol + 2
                If currentCol > 3 Then
                    currentCol = 1
                    currentRow = currentRow + 2
                End If

                ' 重置学生信息
                studentInfo = ""
            End If

            ' 获取对应的学校名称
            Dim tempSchoolName As String
            Dim tempJ As Long
            tempSchoolName = ""
            For tempJ = 2 To lastRow
                If ws.Cells(tempJ, 2).Value = schoolPoint Then
                    tempSchoolName = ws.Cells(tempJ, 1).Value ' 从A列获取学校名称
                    Exit For
                End If
            Next tempJ

            ' 检查是否是新学校
            currentSchoolName = tempSchoolName
            If currentSchoolName <> previousSchoolName Then
                ' 如果不是第一个学校，则添加分节符
                If Not isFirstRecord Then
                    ' 在新学校前添加分页符，确保页码重置
                    wsOutput.HPageBreaks.Add Before:=wsOutput.Cells(currentRow, 1)
                End If

                previousSchoolName = currentSchoolName

                ' 设置页脚
                With wsOutput.PageSetup
                    ' 使用Excel内置页码代码：&P表示当前页码，&N表示总页数
                    .CenterFooter = currentSchoolName & Chr(10) & "第&P页 共&N页"
                End With
            End If

            ' 检查当前行是否在页面底部，如果是，则强制到下一页
            ' 使用更简单的方法：如果当前行超过了一定数量，则强制到下一页
            ' 每页大约能容纳50行（根据实际情况可能会有所不同）
            If (currentRow Mod 40) > 35 Then
                wsOutput.HPageBreaks.Add Before:=wsOutput.Cells(currentRow, 1)
            End If

            wsOutput.Cells(currentRow, 1).Resize(1, 1).Merge
            wsOutput.Cells(currentRow, 1).Value = tempSchoolName & " 校点：" & schoolPoint
            With wsOutput.Cells(currentRow, 1).Font
                .Name = "黑体"
                .Size = 14 ' 四号字大约为12磅
            End With
            wsOutput.Cells(currentRow, 1).HorizontalAlignment = xlCenter

            ' 清除边框
            wsOutput.Cells(currentRow, 1).Borders.LineStyle = xlNone

            ' 设置行高为标准行高的一半
            wsOutput.Cells(currentRow, 1).RowHeight = 32.5 ' 标准行高65的一半
            currentRow = currentRow
            prevSchoolPoint = schoolPoint
            ' 重置上一组信息
            prevExamRoom = 0
            prevSeatNumber = 0
            isFirstRecord = True ' 标记为第一条记录
        End If

        If examRoom <> prevExamRoom Or seatNumber <> prevSeatNumber Then
            ' 如果不是第一条记录，写入上一组信息
            If Not isFirstRecord Then
                ' 合并考场和学生信息
                combinedInfo = "第" & prevExamRoom & "考场 第" & prevSeatNumber & "桌" & vbCrLf & studentInfo
                ' 去除 combinedInfo 末尾的空白字符
                combinedInfo = Trim(combinedInfo)
                ' 写入合并后的信息
                wsOutput.Cells(currentRow, currentCol).Value = combinedInfo


                With wsOutput.Cells(currentRow, currentCol).Font
                    .Name = "黑体"
                    .Size = 14
                End With
                ' 设置单元格内容左对齐
                wsOutput.Cells(currentRow, currentCol).HorizontalAlignment = xlCenter

                ' 设置组信息的虚线边框
                With wsOutput.Cells(currentRow, currentCol).Resize(1, 1).Borders
                    ' 修改为双线样式
                    .LineStyle = xlDouble
                    .ColorIndex = 0 ' 边框颜色
                    .TintAndShade = 0
                    .Weight = xlThin ' 边框粗细
                End With

                ' 调整单元格格式
                wsOutput.Cells(currentRow, currentCol).RowHeight = 65
                wsOutput.Cells(currentRow, currentCol).ColumnWidth = 43

                ' 移动到下一个单元格
                currentCol = currentCol + 2
                If currentCol > 3 Then
                    currentCol = 1
                    currentRow = currentRow + 2
                End If

                ' 重置学生信息
                studentInfo = ""
            End If

            ' 如果不是第一个考场，插入分页符
            If Not isFirstRecord And examRoom > prevExamRoom Then
                ' 确保不影响当前考场和座位信息
                If currentCol = 1 Then
                    wsOutput.HPageBreaks.Add Before:=wsOutput.Cells(currentRow, 1)
                    currentRow = currentRow + 1
                Else
                    wsOutput.HPageBreaks.Add Before:=wsOutput.Cells(currentRow + 1, 1)
                    currentRow = currentRow + 1
                    currentCol = 1
                End If
            End If

            ' 新增逻辑：如果当前座位号为 1 ，换一行从第一列开始
            If seatNumber = 1 Then
                currentCol = 1
                currentRow = currentRow + 2
            End If

            ' 更新上一组信息
            prevExamRoom = examRoom
            prevSeatNumber = seatNumber
            isFirstRecord = False ' 标记不再是第一条记录
        End If

        ' 合并学生信息
        studentInfo = studentInfo & ws.Cells(i, 3).Value & "年级  " & ws.Cells(i, 4).Value & "班" & " 考号：" & ws.Cells(i, 5).Value & " " & ws.Cells(i, 6).Value & vbCrLf
    Next i

    ' 写入最后一组信息
    ' 合并考场和学生信息
    combinedInfo = "第" & prevExamRoom & "考场 第" & prevSeatNumber & "桌" & vbCrLf & studentInfo

    ' 去除 combinedInfo 末尾的空白字符
    combinedInfo = Trim(combinedInfo)
    ' 写入合并后的信息
    wsOutput.Cells(currentRow, currentCol).Value = combinedInfo


    With wsOutput.Cells(currentRow, currentCol).Font
        .Name = "黑体"
        .Size = 14
    End With
    ' 设置单元格内容居中
    wsOutput.Cells(currentRow, currentCol).HorizontalAlignment = xlCenter

    ' 设置组信息的虚线边框
    With wsOutput.Cells(currentRow, currentCol).Resize(1, 1).Borders
        ' 修改为双线样式
        .LineStyle = xlDouble
        .ColorIndex = 0 ' 边框颜色
        .TintAndShade = 0
        .Weight = xlThin ' 边框粗细
    End With

    ' 调整单元格格式
    wsOutput.Cells(currentRow, currentCol).RowHeight = 65
    wsOutput.Cells(currentRow, currentCol).ColumnWidth = 43

    ' 设置整个工作表的打印区域
    wsOutput.PageSetup.PrintArea = wsOutput.UsedRange.Address

    ' 打印设置
    wsOutput.PageSetup.Orientation = xlPortrait
    wsOutput.PageSetup.PaperSize = xlPaperA4

    ' 调整页边距，将厘米转换为磅（1 厘米约等于 28.35 磅）
    wsOutput.PageSetup.TopMargin = Application.CentimetersToPoints(1) ' 减小上边距
    wsOutput.PageSetup.BottomMargin = Application.CentimetersToPoints(1) ' 减小下边距
    wsOutput.PageSetup.LeftMargin = Application.CentimetersToPoints(0.5) ' 减小左边距
    wsOutput.PageSetup.RightMargin = Application.CentimetersToPoints(0.5) ' 减小右边距

    ' 调整缩放比例，使内容刚好在页面内
    wsOutput.PageSetup.FitToPagesWide = 1 ' 调整为一页宽
    wsOutput.PageSetup.FitToPagesTall = False ' 不限制页数

    ' 设置页脚
    With wsOutput.PageSetup
        ' 确保页码按分页符递增
        .DifferentFirstPageHeaderFooter = False
        .ScaleWithDocHeaderFooter = True
        .AlignMarginsHeaderFooter = True
        ' 设置默认页脚，确保所有页面都有页码
        .CenterFooter = Chr(10) & "第&P页 共&N页"
    End With

    ' 打印预览
    wsOutput.PrintPreview

    ' 按考场分开打印
    Dim uniqueExamRooms As Object
    Set uniqueExamRooms = CreateObject("Scripting.Dictionary")
    For i = 2 To lastRow
        examRoom = ws.Cells(i, 7).Value
        If Not uniqueExamRooms.Exists(examRoom) Then
            uniqueExamRooms.Add examRoom, 1
        End If
    Next i

    Dim currentExamRoom As Variant
    For Each currentExamRoom In uniqueExamRooms.Keys
        ' 筛选当前考场的桌角条
        Dim printRange As Range
        Dim firstCell As Range
        Dim lastCell As Range
        Dim cell As Range
        Set printRange = Nothing
        Dim cellVar As Variant
        For Each cellVar In wsOutput.UsedRange
            If InStr(cellVar.Value, "第" & currentExamRoom & "考场") > 0 Then
                If printRange Is Nothing Then
                    Set firstCell = cellVar
                    Set lastCell = cellVar
                    Set printRange = cellVar.Resize(1, 2) ' 合并第1列和第2列
                Else
                    If cellVar.Row > lastCell.Row Then
                        Set lastCell = cellVar
                    End If
                    Set printRange = Union(printRange, cellVar.Resize(1, 2)) ' 合并第1列和第2列
                End If
            End If
        Next cellVar

        If Not printRange Is Nothing Then
            ' 设置打印区域
            wsOutput.PageSetup.PrintArea = printRange.Address

            ' 打印设置
            wsOutput.PageSetup.Orientation = xlPortrait
            wsOutput.PageSetup.PaperSize = xlPaperA4

            ' 调整页边距，将厘米转换为磅（1 厘米约等于 28.35 磅）
            wsOutput.PageSetup.TopMargin = Application.CentimetersToPoints(1) ' 减小上边距
            wsOutput.PageSetup.BottomMargin = Application.CentimetersToPoints(1) ' 减小下边距
            wsOutput.PageSetup.LeftMargin = Application.CentimetersToPoints(0.5) ' 减小左边距
            wsOutput.PageSetup.RightMargin = Application.CentimetersToPoints(0.5) ' 减小右边距

            ' 调整缩放比例，使三列刚好在一面宽度里
            wsOutput.PageSetup.FitToPagesWide = 1 ' 调整为一页宽
            wsOutput.PageSetup.FitToPagesTall = False ' 不限制页数

            ' 打印当前考场的桌角条
'            wsOutput.PrintOut
        End If
    Next currentExamRoom
        For Each sheet In ThisWorkbook.Sheets
            If sheet.Name = "桌角条打印" Then
                Application.DisplayAlerts = False
                sheet.Delete
                Application.DisplayAlerts = True
            End If
        Next sheet
End Sub
