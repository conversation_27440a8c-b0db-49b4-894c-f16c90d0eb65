Option Explicit

' 常量定义
Const COL_SCHOOL As Integer = 1     ' A列：学校
Const COL_GRADE As Integer = 2      ' B列：年级
Const COL_CLASS As Integer = 3      ' C列：班级
Const COL_EXAM_NO As Integer = 4    ' D列：准考号
Const COL_NAME As Integer = 5       ' E列：姓名
Const COL_ROOM As Integer = 6       ' F列：考场号
Const COL_SEAT As Integer = 7       ' G列：座位号
Const COL_RANDOM As Integer = 8     ' H列：随机数
Const COL_CLASS_ORDER As Integer = 9 ' I列：班内序号

Sub ArrangeExamRoomsAndPrint()
    Dim ws As Worksheet
    Dim wsDoorStickers As Worksheet
    Dim wsCornerStickers As Worksheet
    Dim wsClassSchedule As Worksheet
    Dim lastRow As Long, i As Long, j As Long
    Dim roomSize As Integer
    Dim dataArr() As Variant
    Dim currentRoom As Integer, currentSeat As Integer
    Dim currentGrade As String, prevGrade As String
    
    ' 禁止屏幕更新
    Application.ScreenUpdating = False
    ' 设置工作表
    Set ws = Sheets("学生考场编排表")

    ' 获取用户输入的考场规格
    roomSize = Application.InputBox("请输入考场规格（25或30人）:", "考场编排", Type:=1)
    If roomSize <> 25 And roomSize <> 30 Then
        MsgBox "请输入有效的考场规格（25或30）!", vbExclamation
        Exit Sub
    End If

    ' 获取数据范围
    lastRow = ws.Cells(ws.Rows.Count, COL_SCHOOL).End(xlUp).Row
    If lastRow < 2 Then
        MsgBox "没有找到学生数据!", vbExclamation
        Exit Sub
    End If

    ' 将数据加载到数组
    ReDim dataArr(1 To lastRow - 1, 1 To COL_CLASS_ORDER)
    For i = 2 To lastRow
        For j = 1 To COL_CLASS_ORDER
            If j <= 5 Then
                dataArr(i - 1, j) = ws.Cells(i, j).Value
            ElseIf j = COL_RANDOM Then
                ' 生成0-1之间的随机数
                dataArr(i - 1, j) = Rnd()
            End If
        Next j
    Next i

    ' 第一次排序：年级升序、班级升序
    QuickSortMulti dataArr, COL_GRADE, COL_CLASS

    ' 第二次排序：年级升序、班级升序、随机数升序，生成班内序号
    QuickSortMulti dataArr, COL_GRADE, COL_CLASS, COL_RANDOM

    ' 生成班内序号
    Dim classCount As Integer
    Dim currentClass As String
    classCount = 1
    currentGrade = dataArr(1, COL_GRADE)
    currentClass = dataArr(1, COL_CLASS)

    For i = 1 To UBound(dataArr, 1)
        If dataArr(i, COL_GRADE) <> currentGrade Or dataArr(i, COL_CLASS) <> currentClass Then
            currentGrade = dataArr(i, COL_GRADE)
            currentClass = dataArr(i, COL_CLASS)
            classCount = 1
        End If
        dataArr(i, COL_CLASS_ORDER) = classCount
        classCount = classCount + 1
    Next i

    ' 第三次排序：年级升序、班内序号升序
    QuickSortMulti dataArr, COL_GRADE, COL_CLASS_ORDER

    ' 分配考场号和座位号 - 修正后的逻辑
    currentRoom = 1
    currentSeat = 1
    prevGrade = dataArr(1, COL_GRADE)

    Dim studentIndex As Long
    studentIndex = 1

    ' 处理所有学生
    While studentIndex <= UBound(dataArr, 1)
        ' 收集当前考场的学生
        Dim roomStudents() As Variant
        ReDim roomStudents(1 To roomSize, 1 To COL_CLASS_ORDER)
        Dim roomCount As Integer
        roomCount = 0
        
        ' 填充当前考场
        While roomCount < roomSize And studentIndex <= UBound(dataArr, 1)
            ' 检查是否需要换年级（新考场）
            If dataArr(studentIndex, COL_GRADE) <> prevGrade Then
                If roomCount > 0 Then
                    ' 处理未满的考场
                    AssignRoomAndSeats dataArr, studentIndex - roomCount, currentRoom, roomStudents, roomCount
                End If
                currentRoom = currentRoom + 1
                prevGrade = dataArr(studentIndex, COL_GRADE)
                Exit While
            End If
            
            ' 添加学生到当前考场
            roomCount = roomCount + 1
            For j = 1 To COL_CLASS_ORDER
                roomStudents(roomCount, j) = dataArr(studentIndex, j)
            Next j
            
            studentIndex = studentIndex + 1
        Wend
        
        ' 处理当前考场的学生
        If roomCount > 0 Then
            AssignRoomAndSeats dataArr, studentIndex - roomCount, currentRoom, roomStudents, roomCount
            currentRoom = currentRoom + 1
        End If
    Wend

    ' 将结果写回工作表
    For i = 1 To UBound(dataArr, 1)
        For j = 1 To COL_CLASS_ORDER
            ws.Cells(i + 1, j).Value = dataArr(i, j)
        Next j
    Next i
    ' 删除H列和I列的内容
    ws.Columns("H:I").ClearContents
    ' 屏幕更新
    Application.ScreenUpdating = True
    MsgBox "考场编排完成!", vbInformation
End Sub

Sub GenerateDoorStickers(ws As Worksheet, dataArr() As Variant, roomSize As Integer)
    Dim i As Long, j As Long
    Dim currentRoom As Integer
    Dim rowIndex As Integer

    currentRoom = 1
    rowIndex = 1

    For i = 1 To UBound(dataArr, 1)
        If dataArr(i, COL_ROOM) <> currentRoom Then
            ' 新考场开始
            currentRoom = dataArr(i, COL_ROOM)
            rowIndex = rowIndex + 2 ' 留出空行
            ws.Cells(rowIndex, 1).Value = "考场号: " & currentRoom
            ws.Cells(rowIndex, 1).Font.Bold = True
            rowIndex = rowIndex + 1
        End If

        ws.Cells(rowIndex, 1).Value = "座位号: " & dataArr(i, COL_SEAT)
        ws.Cells(rowIndex, 2).Value = "班级: " & dataArr(i, COL_CLASS)
        ws.Cells(rowIndex, 3).Value = "姓名: " & dataArr(i, COL_NAME)
        ws.Cells(rowIndex, 4).Value = "准考号: " & dataArr(i, COL_EXAM_NO)

        rowIndex = rowIndex + 1
    Next i
End Sub

Sub GenerateCornerStickers(ws As Worksheet, dataArr() As Variant)
    Dim i As Long
    Dim rowIndex As Integer

    rowIndex = 1

    For i = 1 To UBound(dataArr, 1)
        ws.Cells(rowIndex, 1).Value = "班级: " & dataArr(i, COL_CLASS)
        ws.Cells(rowIndex + 1, 1).Value = "准考号: " & dataArr(i, COL_EXAM_NO)
        ws.Cells(rowIndex + 2, 1).Value = "姓名: " & dataArr(i, COL_NAME)
        ws.Cells(rowIndex, 2).Value = "考场号: " & dataArr(i, COL_ROOM)
        ws.Cells(rowIndex + 1, 2).Value = "座位号: " & dataArr(i, COL_SEAT)

        rowIndex = rowIndex + 3
    Next i
End Sub

Sub GenerateClassSchedule(ws As Worksheet, dataArr() As Variant)
    Dim i As Long
    Dim currentClass As String
    Dim rowIndex As Integer

    currentClass = ""
    rowIndex = 1

    For i = 1 To UBound(dataArr, 1)
        If dataArr(i, COL_CLASS) <> currentClass Then
            currentClass = dataArr(i, COL_CLASS)
            rowIndex = rowIndex + 2 ' 留出空行
            ws.Cells(rowIndex, 1).Value = "班级: " & currentClass
            ws.Cells(rowIndex, 1).Font.Bold = True
            rowIndex = rowIndex + 1
        End If

        ws.Cells(rowIndex, 1).Value = "考场号: " & dataArr(i, COL_ROOM)
        ws.Cells(rowIndex, 2).Value = "座位号: " & dataArr(i, COL_SEAT)

        rowIndex = rowIndex + 1
    Next i
End Sub

' 快速排序算法（支持多列排序）
Sub QuickSortMulti(arr() As Variant, col1 As Integer, Optional col2 As Integer = -1, Optional col3 As Integer = -1)
    Dim cols(1 To 3) As Integer
    cols(1) = col1
    cols(2) = col2
    cols(3) = col3
    QuickSortMultiInternal arr, LBound(arr), UBound(arr), cols
End Sub

Sub QuickSortMultiInternal(arr() As Variant, ByVal low As Long, ByVal high As Long, cols() As Integer)
    If low < high Then
        Dim pivot As Long
        pivot = PartitionMulti(arr, low, high, cols)
        QuickSortMultiInternal arr, low, pivot - 1, cols
        QuickSortMultiInternal arr, pivot + 1, high, cols
    End If
End Sub

Function PartitionMulti(arr() As Variant, ByVal low As Long, ByVal high As Long, cols() As Integer) As Long
    Dim pivot As Variant
    pivot = arr(high, cols(1))

    Dim i As Long
    i = low - 1

    Dim j As Long
    For j = low To high - 1
        If CompareRows(arr, j, high, cols) <= 0 Then
            i = i + 1
            SwapRows arr, i, j
        End If
    Next j

    SwapRows arr, i + 1, high
    PartitionMulti = i + 1
End Function

Function CompareRows(arr() As Variant, row1 As Long, row2 As Long, cols() As Integer) As Integer
    Dim i As Integer
    For i = 1 To UBound(cols)
        If cols(i) = -1 Then Exit For  ' 跳过无效排序列
        If arr(row1, cols(i)) < arr(row2, cols(i)) Then
            CompareRows = -1
            Exit Function
        ElseIf arr(row1, cols(i)) > arr(row2, cols(i)) Then
            CompareRows = 1
            Exit Function
        End If
    Next i
    CompareRows = 0
End Function

Sub SwapRows(arr() As Variant, row1 As Long, row2 As Long)
    If row1 = row2 Then Exit Sub

    Dim temp As Variant
    Dim i As Integer
    For i = LBound(arr, 2) To UBound(arr, 2)
        temp = arr(row1, i)
        arr(row1, i) = arr(row2, i)
        arr(row2, i) = temp
    Next i
End Sub

' 新增子程序：分配考场座位，避免同班同学相邻
Sub AssignRoomAndSeats(ByRef dataArr() As Variant, startIndex As Long, roomNumber As Integer, roomStudents() As Variant, studentCount As Integer)
    Dim i As Integer, j As Integer, k As Integer, m As Integer
    Dim temp As Variant
    
    ' 首先，对考场内的学生进行随机排序以打乱原有顺序
    Randomize
    For i = 1 To studentCount
        j = Int((studentCount * Rnd) + 1)
        If i <> j Then
            For k = 1 To COL_CLASS_ORDER
                temp = roomStudents(i, k)
                roomStudents(i, k) = roomStudents(j, k)
                roomStudents(j, k) = temp
            Next k
        End If
    Next i
    
    ' 检查并调整相邻座位的学生
    For i = 1 To studentCount - 1
        ' 如果发现相邻的学生来自同一班级
        If roomStudents(i, COL_CLASS) = roomStudents(i + 1, COL_CLASS) Then
            ' 寻找来自不同班级的学生进行交换
            For k = i + 2 To studentCount
                If k <= studentCount And roomStudents(k, COL_CLASS) <> roomStudents(i, COL_CLASS) Then
                    ' 交换位置
                    For m = 1 To COL_CLASS_ORDER
                        temp = roomStudents(i + 1, m)
                        roomStudents(i + 1, m) = roomStudents(k, m)
                        roomStudents(k, m) = temp
                    Next m
                    Exit For
                End If
            Next k
        End If
    Next i
    
    ' 将优化后的座位分配写回主数组
    For i = 1 To studentCount
        dataArr(startIndex + i - 1, COL_ROOM) = roomNumber
        dataArr(startIndex + i - 1, COL_SEAT) = i
        For j = 1 To COL_CLASS_ORDER
            dataArr(startIndex + i - 1, j) = roomStudents(i, j)
        Next j
    Next i
End Sub